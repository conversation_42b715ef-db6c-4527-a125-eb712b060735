function u(e){return u.result?u.result:!e||typeof e.getSerializer!="function"?Promise.reject(new Error("localforage.getSerializer() was not available! localforage v1.4+ is required!")):(u.result=e.getSerializer(),u.result)}function i(e,r){return i.result=i.result||{},i.result[r]?i.result[r]:!e||typeof e.getDriver!="function"?Promise.reject(new Error("localforage.getDriver() was not available! localforage v1.4+ is required!")):(i.result[r]=e.getDriver(r),i.result[r])}function d(e){return i(e,e.WEBSQL)}var y=new Promise(function(e,r){typeof sqlitePlugin<"u"?e():typeof cordova>"u"?r(new Error("cordova is not defined.")):document.addEventListener("deviceready",function(){return e()},!1)}),P=y.catch(function(){return Promise.resolve()});function g(){return P.then(function(){if(typeof sqlitePlugin<"u"&&typeof sqlitePlugin.openDatabase=="function")return sqlitePlugin.openDatabase;throw new Error("SQLite plugin is not present.")})}function S(e){var r=this,t={db:null};if(e)for(var n in e)t[n]=typeof e[n]!="string"?e[n].toString():e[n];var a=g().then(function(o){return new Promise(function(c,f){try{t.location=t.location||"default",t.db=o({name:t.name,version:String(t.version),description:t.description,size:t.size,key:t.dbKey,location:t.location})}catch(v){f(v)}t.db.transaction(function(v){v.executeSql("CREATE TABLE IF NOT EXISTS "+t.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],function(){r._dbInfo=t,c()},function(h,p){f(p)})})})}),l=u(r),s=d(r);return Promise.all([l,s,a]).then(function(o){return t.serializer=o[0],a})}var m={_driver:"cordovaSQLiteDriver",_initStorage:S,_support:function(){return g().then(function(r){return!!r}).catch(function(){return!1})}};function b(e){var r=["clear","getItem","iterate","key","keys","length","removeItem","setItem","dropInstance"];function t(l,s){l[s]=function(){var o=this,c=arguments;return d(o).then(function(f){return f[s].apply(o,c)})}}for(var n=0,a=r.length;n<a;n++)t(e,r[n])}b(m);export{m as default};
