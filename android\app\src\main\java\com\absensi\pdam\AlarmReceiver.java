package com.absensi.pdam;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.media.MediaPlayer;
import android.os.Build;

public class AlarmReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null || intent.getAction() == null) return;

        String action = intent.getAction();

        // Putar suara sesuai action. Pastikan file audio diletakkan di res/raw
        // dan namanya sesuai (misal: masuk.mp3, pulang.mp3 -> R.raw.masuk, R.raw.pulang)
        int soundResId = 0;
        if (AlarmScheduler.ACTION_ABSEN_MASUK.equals(action)) {
            soundResId = context.getResources().getIdentifier("masuk", "raw", context.getPackageName());
        } else if (AlarmScheduler.ACTION_ABSEN_PULANG.equals(action)) {
            soundResId = context.getResources().getIdentifier("pulang", "raw", context.getPackageName());
        }

        if (soundResId != 0) {
            try {
                MediaPlayer mp = MediaPlayer.create(context, soundResId);
                if (mp != null) {
                    mp.setOnCompletionListener(MediaPlayer::release);
                    mp.start();
                }
            } catch (Exception ignored) {}
        }

        // Jadwalkan ulang alarm berikutnya agar berulang setiap hari kerja sesuai aturan
        if (AlarmScheduler.ACTION_ABSEN_MASUK.equals(action)) {
            AlarmScheduler.scheduleMasuk(context);
        } else if (AlarmScheduler.ACTION_ABSEN_PULANG.equals(action)) {
            AlarmScheduler.schedulePulang(context);
        }
    }
}


