/* Lembur Page Styles */

.lembur-active-card {
  margin: 16px;
  border-left: 4px solid var(--ion-color-danger);
  background: linear-gradient(135deg, var(--ion-color-primary-tint) 0%, var(--ion-color-success-shade) 100%);
  color: white;
}

.lembur-status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.status-icon {
  font-size: 24px;
}

.status-icon.active {
  color: var(--ion-color-light);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.lembur-status-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.lembur-status-header p {
  margin: 4px 0 0 0;
  font-size: 14px;
  opacity: 0.9;
}

.lembur-details {
  background: rgba(255, 255, 255, 0.1);
  padding: 12px;
  border-radius: 8px;
}

.lembur-details p {
  margin: 8px 0;
  font-size: 14px;
}

.form-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--ion-color-light);
}

.form-icon {
  font-size: 24px;
  color: var(--ion-color-primary);
}

.form-icon.finish {
  color: var(--ion-color-success);
}

.form-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.readonly-input {
  background-color: var(--ion-color-light);
  opacity: 0.7;
}

.photo-section {
  margin: 20px 0;
}

.photo-section ion-label {
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--ion-color-dark);
}

.photo-preview {
  position: relative;
  display: inline-block;
  margin-top: 8px;
}

.photo-preview img {
  width: 200px;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid var(--ion-color-light);
}

.remove-photo-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--ion-color-danger);
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  --padding-start: 0;
  --padding-end: 0;
}

.camera-btn {
  margin-top: 8px;
  --border-color: var(--ion-color-primary);
  --color: var(--ion-color-primary);
}

.submit-btn {
  margin-top: 24px;
  --background: var(--ion-color-primary);
  font-weight: 600;
}

.submit-btn.finish {
  --background: var(--ion-color-success);
}

.submit-btn:disabled {
  opacity: 0.5;
}

/* History Styles */
.history-item {
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
  margin-bottom: 12px;
  border-bottom: 1px solid var(--ion-color-light);
}

.history-content {
  width: 100%;
  padding: 12px 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.history-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.history-content p {
  margin: 8px 0;
  font-size: 14px;
  color: var(--ion-color-medium);
}

.time-info {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.time-info span {
  font-size: 12px;
  color: var(--ion-color-medium);
  background: var(--ion-color-light);
  padding: 4px 8px;
  border-radius: 4px;
}

/* Camera Modal Styles */
.camera-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: black;
}

.camera-video {
  flex: 1;
  width: 100%;
  object-fit: cover;
}

.camera-controls {
  padding: 20px;
  background: rgba(0, 0, 0, 0.8);
}

.capture-btn {
  --background: var(--ion-color-primary);
  --color: white;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .time-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .time-info span {
    text-align: center;
  }
  
  .photo-preview img {
    width: 100%;
    max-width: 300px;
    height: auto;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .form-header h2 {
    color: var(--ion-color-light);
  }
  
  .history-header h3 {
    color: var(--ion-color-light);
  }
  
  .photo-section ion-label {
    color: var(--ion-color-light);
  }
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* Animation for cards */
ion-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Status badges */
ion-badge {
  font-size: 12px;
  font-weight: 600;
}

/* Form validation styles */
ion-item.item-has-error {
  --border-color: var(--ion-color-danger);
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 12px;
  margin-top: 4px;
  margin-left: 16px;
}
