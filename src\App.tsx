import React, { useEffect, Suspense, lazy } from 'react';
import { Redirect, Route, useHistory } from 'react-router-dom';
import { IonApp, IonRouterOutlet, setupIonicReact, IonSpinner, IonContent } from '@ionic/react';
import { IonReactRouter } from '@ionic/react-router';

// Import halaman utama yang diperlukan saat startup
import Home from './pages/Home';
import Login from './pages/Login';

// Lazy load halaman yang tidak diperlukan saat startup
const Absensi = lazy(() => import('./pages/Absensi'));
const Histori = lazy(() => import('./pages/Histori'));
const IzinDinas = lazy(() => import('./pages/IzinDinas'));
const HistoriIzinDinas = lazy(() => import('./pages/HistoriIzinDinas'));
const Rapat = lazy(() => import('./pages/Rapat'));
const LaporanHarian = lazy(() => import('./pages/LaporanHarian'));
const Profile = lazy(() => import('./pages/Profile'));
const Lembur = lazy(() => import('./pages/Lembur'));
const BarcodeTest = lazy(() => import('./components/BarcodeTest'));
const GantiPassword = lazy(() => import('./pages/ganti_password'));

// Lazy load utility functions
const fetchAndStoreLokasi = lazy(() => import('./utils/lokasi').then(module => ({ default: module.fetchAndStoreLokasi })));
const fetchAndStoreBidang = lazy(() => import('./utils/bidang').then(module => ({ default: module.fetchAndStoreBidang })));
const fetchAndStoreJamKerja = lazy(() => import('./utils/jamKerja').then(module => ({ default: module.fetchAndStoreJamKerja })));
const fetchAndStoreJamKerjaBidang = lazy(() => import('./utils/jamKerjaBidang').then(module => ({ default: module.fetchAndStoreJamKerjaBidang })));

/* Core CSS required for Ionic components to work properly */
import '@ionic/react/css/core.css';

/* Basic CSS for apps built with Ionic */
import '@ionic/react/css/normalize.css';
import '@ionic/react/css/structure.css';
import '@ionic/react/css/typography.css';

/* Optional CSS utils that can be commented out */
import '@ionic/react/css/padding.css';
import '@ionic/react/css/float-elements.css';
import '@ionic/react/css/text-alignment.css';
import '@ionic/react/css/text-transformation.css';
import '@ionic/react/css/flex-utils.css';
import '@ionic/react/css/display.css';

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* import '@ionic/react/css/palettes/dark.always.css'; */
/* import '@ionic/react/css/palettes/dark.class.css'; */
import '@ionic/react/css/palettes/dark.system.css';

/* Theme variables */
import './theme/variables.css';

setupIonicReact();

const PrivateRoute = ({ children, ...rest }: any) => {
  const isLoggedIn = !!localStorage.getItem('user');
  return (
    <Route
      {...rest}
      render={({ location }) =>
        isLoggedIn ? (
          children
        ) : (
          <Redirect to={{ pathname: '/login', state: { from: location } }} />
        )
      }
    />
  );
};

const Logout: React.FC = () => {
  useEffect(() => {
    localStorage.removeItem('user');
  }, []);
  return <Redirect to="/login" />;
};

// Loading component untuk lazy loaded pages
const PageLoader: React.FC = () => (
  <IonContent className="ion-padding">
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
      <IonSpinner name="crescent" />
    </div>
  </IonContent>
);

const App: React.FC = () => {
  useEffect(() => {
    // Defer data fetching untuk mengurangi loading time awal
    const initializeData = async () => {
      try {
        // Load utilities secara lazy
        const [lokasiModule, bidangModule, jamKerjaModule, jamKerjaBidangModule] = await Promise.all([
          import('./utils/lokasi'),
          import('./utils/bidang'),
          import('./utils/jamKerja'),
          import('./utils/jamKerjaBidang')
        ]);

        // Execute data fetching dengan delay untuk tidak memblokir UI
        setTimeout(() => {
          lokasiModule.fetchAndStoreLokasi();
          bidangModule.fetchAndStoreBidang();
          jamKerjaModule.fetchAndStoreJamKerja();
          jamKerjaBidangModule.fetchAndStoreJamKerjaBidang();
        }, 1000); // Delay 1 detik setelah app load
      } catch (error) {
        console.log('Error loading data utilities:', error);
      }
    };

    initializeData();
  }, []);

  // Dengarkan event dari native (MainActivity) untuk pelanggaran perangkat
  useEffect(() => {
    const getNormalizedUser = () => {
      try {
        const raw = localStorage.getItem('user');
        const parsed = raw ? JSON.parse(raw) : {};
        return Array.isArray(parsed) ? (parsed[0] || {}) : parsed;
      } catch {
        return {} as any;
      }
    };

    const shouldThrottle = (key: string, thresholdMs: number) => {
      try {
        const last = localStorage.getItem(key);
        if (!last) return false;
        const diff = Date.now() - new Date(last).getTime();
        return diff < thresholdMs;
      } catch {
        return false;
      }
    };

    const markSent = (key: string) => {
      try { localStorage.setItem(key, new Date().toISOString()); } catch {}
    };

    const reportViolation = async (reason: string, typeKey: string) => {
      const throttleKey = `violation_sent_${typeKey}`;
      if (shouldThrottle(throttleKey, 5 * 60 * 1000)) {
        return; // hindari spam dalam 5 menit
      }

      const user: any = getNormalizedUser();
      const deviceId = localStorage.getItem('device_id') || '';
      const payload = {
        api_key: 'absensiku_api_key_2023',
        user_id: user?.id || user?.nik || '',
        nik: user?.nik || '',
        device_id: deviceId,
        alasan: reason,
      };

      try {
        await fetch('https://absensiku.trunois.my.id/api/blokir_device.php?api_key=absensiku_api_key_2023', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
        markSent(throttleKey);
      } catch (e) {
        // diamkan saja, akan dicoba lagi ketika event terjadi lagi setelah throttle
      }
    };

    const onFakeGps = (_e: Event) => {
      reportViolation('Penggunaan Fake GPS terdeteksi', 'fake_gps');
    };
    const onAutoTimeOff = (_e: Event) => {
      reportViolation('Pengaturan waktu otomatis dimatikan', 'auto_time_off');
    };

    window.addEventListener('fakeGpsDetected', onFakeGps as EventListener);
    window.addEventListener('autoTimeDisabled', onAutoTimeOff as EventListener);

    return () => {
      window.removeEventListener('fakeGpsDetected', onFakeGps as EventListener);
      window.removeEventListener('autoTimeDisabled', onAutoTimeOff as EventListener);
    };
  }, []);

  return (
  <IonApp>
    <IonReactRouter>
      <IonRouterOutlet>
          <PrivateRoute exact path="/home">
            <Home />
          </PrivateRoute>
          <PrivateRoute exact path="/absensi">
            <Suspense fallback={<PageLoader />}>
              <Absensi />
            </Suspense>
          </PrivateRoute>
          <PrivateRoute exact path="/histori">
            <Suspense fallback={<PageLoader />}>
              <Histori />
            </Suspense>
          </PrivateRoute>
          <PrivateRoute exact path="/izin-dinas">
            <Suspense fallback={<PageLoader />}>
              <IzinDinas />
            </Suspense>
          </PrivateRoute>
          <PrivateRoute exact path="/histori-izin-dinas">
            <Suspense fallback={<PageLoader />}>
              <HistoriIzinDinas />
            </Suspense>
          </PrivateRoute>
          <PrivateRoute exact path="/rapat">
            <Suspense fallback={<PageLoader />}>
              <Rapat />
            </Suspense>
          </PrivateRoute>
          <PrivateRoute exact path="/laporan-harian">
            <Suspense fallback={<PageLoader />}>
              <LaporanHarian />
            </Suspense>
          </PrivateRoute>
          <PrivateRoute exact path="/profile">
            <Suspense fallback={<PageLoader />}>
              <Profile />
            </Suspense>
          </PrivateRoute>
          <PrivateRoute exact path="/lembur">
            <Suspense fallback={<PageLoader />}>
              <Lembur />
            </Suspense>
          </PrivateRoute>
          <PrivateRoute exact path="/barcode-test">
            <Suspense fallback={<PageLoader />}>
              <BarcodeTest />
            </Suspense>
          </PrivateRoute>
          <PrivateRoute exact path="/ganti-password">
            <Suspense fallback={<PageLoader />}>
              <GantiPassword />
            </Suspense>
          </PrivateRoute>
          <Route exact path="/logout" render={() => {
            localStorage.removeItem('user');
            window.location.replace('/login');
            return null;
          }} />
          <Route exact path="/login" render={() => {
            const isLoggedIn = !!localStorage.getItem('user');
            return isLoggedIn ? <Redirect to="/home" /> : <Login />;
          }} />
        <Route exact path="/">
            <Redirect to="/login" />
        </Route>
      </IonRouterOutlet>
    </IonReactRouter>
  </IonApp>
);
};

export default App;
