/* Page base */
.profile-page {
  --background: linear-gradient(180deg, rgba(56, 128, 255, 0.12) 0%, rgba(0,0,0,0) 40%);
}

/* Hero section */
.profile-hero {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 16px 16px;
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
  color: white;
  border-bottom-left-radius: 28px;
  border-bottom-right-radius: 28px;
}

.avatar-wrap {
  width: 128px;
  height: 128px;
  border-radius: 50%;
  padding: 4px;
  background: linear-gradient(135deg, #fff, rgba(255,255,255,0.2));
  box-shadow: 0 10px 30px rgba(0,0,0,0.25);
}

.avatar-wrap.skeleton {
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border: 4px solid rgba(255,255,255,0.85);
}

.default-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255,255,255,0.2);
  color: white;
  font-size: 48px;
  border-radius: 50%;
  font-weight: 600;
}

.profile-name {
  margin: 12px 0 0;
  font-size: 22px;
  font-weight: 700;
}

.profile-role {
  margin: 4px 0 0;
  opacity: 0.9;
}

.chip-wrap {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.profile-chip {
  background: rgba(255,255,255,0.16);
  color: #fff;
  border-radius: 16px;
}

/* Card */
.profile-card {
  margin: 20px 16px;
  border-radius: 16px;
}

.glass {
  background: rgba(255,255,255,0.85);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.08);
}

.info-list {
  margin-top: 4px;
}

.info-item {
  --background: transparent;
  margin: 6px 0;
  border-radius: 12px;
}

.info-icon {
  color: var(--ion-color-primary);
  font-size: 20px;
}

.label {
  margin: 0;
  font-size: 12px;
  color: var(--ion-color-medium);
}

.value {
  margin: 4px 0 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.action-group {
  margin-top: 12px;
}

/* Schedule styling */
.schedule-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.schedule-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  padding: 6px 10px;
  border-radius: 8px;
  width: fit-content;
}

.schedule-badge ion-icon {
  font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .schedule-info {
    gap: 6px;
  }
  
  .schedule-badge {
    font-size: 11px;
    padding: 5px 8px;
  }
}