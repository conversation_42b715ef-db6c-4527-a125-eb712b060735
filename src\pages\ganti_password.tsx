import { 
    IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonItem, IonLabel, IonInput, IonButton, IonAlert, IonLoading 
  } from '@ionic/react';
  import { useState } from 'react';
  
  const GantiPassword: React.FC = () => {
    const [oldPassword, setOldPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');
    const [loading, setLoading] = useState(false);
  
    const handleSubmit = async () => {
      if (!oldPassword || !newPassword || !confirmPassword) {
        setAlertMessage('Semua field wajib diisi');
        setShowAlert(true);
        return;
      }
  
      if (newPassword !== confirmPassword) {
        setAlertMessage('Password baru dan konfirmasi password tidak sama');
        setShowAlert(true);
        return;
      }
  
      setLoading(true);
  
      try {
        const userData = localStorage.getItem('user');
        if (!userData) {
          setAlertMessage('Data pengguna tidak ditemukan, silakan login kembali');
          setShowAlert(true);
          setLoading(false);
          return;
        }
  
        const parsed = JSON.parse(userData);
        const user = Array.isArray(parsed) ? parsed[0] : parsed;
  
        const response = await fetch(`https://absensiku.trunois.my.id/api/karyawan.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(user.nik)}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                nik: user.nik,
                old_password: oldPassword,
                new_password: newPassword
            })
        });
        
  
        const data = await response.json();
  
        if (data.status === 'success') {
          setAlertMessage('Password berhasil diubah');
          setShowAlert(true);
          setOldPassword('');
          setNewPassword('');
          setConfirmPassword('');
        } else {
          setAlertMessage(data.message || 'Gagal mengganti password');
          setShowAlert(true);
        }
      } catch (error) {
        setAlertMessage('Terjadi kesalahan koneksi ke server');
        setShowAlert(true);
      } finally {
        setLoading(false);
      }
    };
  
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Ganti Password</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent className="ion-padding">
          <IonItem>
            <IonLabel position="floating">Password Lama</IonLabel>
            <IonInput
              type="password"
              value={oldPassword}
              onIonChange={e => setOldPassword(e.detail.value!)}
            />
          </IonItem>
          <IonItem>
            <IonLabel position="floating">Password Baru</IonLabel>
            <IonInput
              type="password"
              value={newPassword}
              onIonChange={e => setNewPassword(e.detail.value!)}
            />
          </IonItem>
          <IonItem>
            <IonLabel position="floating">Konfirmasi Password Baru</IonLabel>
            <IonInput
              type="password"
              value={confirmPassword}
              onIonChange={e => setConfirmPassword(e.detail.value!)}
            />
          </IonItem>
  
          <IonButton expand="block" color="primary" style={{ marginTop: '20px' }} onClick={handleSubmit}>
            Simpan Perubahan
          </IonButton>
  
          <IonLoading isOpen={loading} message="Memproses..." />
          <IonAlert
            isOpen={showAlert}
            onDidDismiss={() => setShowAlert(false)}
            header="Informasi"
            message={alertMessage}
            buttons={['OK']}
          />
        </IonContent>
      </IonPage>
    );
  };
  
  export default GantiPassword;
  