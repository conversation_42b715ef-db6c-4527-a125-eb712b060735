.loading-screen {
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
  max-width: 300px;
  width: 100%;
}

.loading-logo {
  margin-bottom: 2rem;
  animation: fadeInScale 0.8s ease-out;
}

.logo-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: white;
  padding: 8px;
}

.loading-spinner {
  margin-bottom: 1.5rem;
  animation: fadeIn 1s ease-out 0.3s both;
}

.loading-spinner ion-spinner {
  width: 40px;
  height: 40px;
}

.progress-container {
  width: 100%;
  margin-bottom: 1.5rem;
  animation: fadeIn 1s ease-out 0.6s both;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 2px;
  transition: width 0.3s ease;
  animation: shimmer 2s infinite;
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.loading-message {
  margin-bottom: 2rem;
  animation: fadeIn 1s ease-out 0.9s both;
}

.loading-message p {
  font-size: 1rem;
  font-weight: 500;
  color: white;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.loading-tips {
  animation: fadeIn 1s ease-out 1.2s both;
}

.tip-text {
  opacity: 0.8;
}

.tip-text small {
  font-size: 0.75rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .loading-container {
    padding: 1.5rem;
    max-width: 280px;
  }
  
  .logo-image {
    width: 60px;
    height: 60px;
  }
  
  .loading-spinner ion-spinner {
    width: 32px;
    height: 32px;
  }
  
  .loading-message p {
    font-size: 0.9rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .loading-screen {
    --background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .loading-container * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
