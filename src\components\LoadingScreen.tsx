import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>nt, IonSpinner, IonText } from '@ionic/react';
import './LoadingScreen.css';

interface LoadingScreenProps {
  message?: string;
  showProgress?: boolean;
  onTimeout?: () => void;
  timeout?: number;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = 'Memuat aplikasi...', 
  showProgress = false,
  onTimeout,
  timeout = 10000 // 10 detik default timeout
}) => {
  const [progress, setProgress] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(message);

  useEffect(() => {
    let progressInterval: NodeJS.Timeout;
    let timeoutId: NodeJS.Timeout;

    if (showProgress) {
      // Simulasi progress loading yang smooth
      progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) return prev; // Stop di 90% sampai loading selesai
          return prev + Math.random() * 15; // Increment random untuk efek natural
        });
      }, 200);
    }

    // Timeout handler
    if (onTimeout && timeout > 0) {
      timeoutId = setTimeout(() => {
        onTimeout();
      }, timeout);
    }

    // Update pesan loading secara bertahap
    const messages = [
      'Memuat aplikasi...',
      'Menyiapkan komponen...',
      'Menginisialisasi data...',
      'Hampir selesai...'
    ];

    let messageIndex = 0;
    const messageInterval = setInterval(() => {
      if (messageIndex < messages.length - 1) {
        messageIndex++;
        setCurrentMessage(messages[messageIndex]);
      }
    }, 1500);

    return () => {
      if (progressInterval) clearInterval(progressInterval);
      if (timeoutId) clearTimeout(timeoutId);
      clearInterval(messageInterval);
    };
  }, [showProgress, onTimeout, timeout]);

  return (
    <IonContent className="loading-screen">
      <div className="loading-container">
        {/* Logo atau branding */}
        <div className="loading-logo">
          <img 
            src="/assets/logo192.png" 
            alt="Logo" 
            className="logo-image"
            onError={(e) => {
              // Fallback jika logo tidak ada
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
        </div>

        {/* Spinner */}
        <div className="loading-spinner">
          <IonSpinner name="crescent" color="primary" />
        </div>

        {/* Progress bar (opsional) */}
        {showProgress && (
          <div className="progress-container">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${Math.min(progress, 100)}%` }}
              />
            </div>
            <IonText color="medium" className="progress-text">
              {Math.round(Math.min(progress, 100))}%
            </IonText>
          </div>
        )}

        {/* Loading message */}
        <IonText color="medium" className="loading-message">
          <p>{currentMessage}</p>
        </IonText>

        {/* Tips untuk user */}
        <div className="loading-tips">
          <IonText color="light" className="tip-text">
            <small>
              💡 Tip: Pastikan koneksi internet stabil untuk pengalaman terbaik
            </small>
          </IonText>
        </div>
      </div>
    </IonContent>
  );
};

export default LoadingScreen;
