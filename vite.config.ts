/// <reference types="vitest" />

import legacy from '@vitejs/plugin-legacy'
import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Optimasi React untuk production
      babel: {
        plugins: [
          // Remove console.log in production
          process.env.NODE_ENV === 'production' && ['transform-remove-console', { exclude: ['error', 'warn'] }]
        ].filter(Boolean)
      }
    }),
    legacy({
      // Target browser yang lebih spesifik untuk mengurangi polyfill
      targets: ['defaults', 'not IE 11']
    })
  ],
  build: {
    // Optimasi build untuk performa
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info']
      }
    },
    rollupOptions: {
      output: {
        // Code splitting manual untuk chunk yang lebih optimal
        manualChunks: {
          // Vendor libraries dalam chunk terpisah
          'ionic-react': ['@ionic/react', '@ionic/react-router'],
          'react-vendor': ['react', 'react-dom', 'react-router', 'react-router-dom'],
          'capacitor': ['@capacitor/core', '@capacitor/camera', '@capacitor/geolocation'],
          'maps': ['@react-google-maps/api', 'react-leaflet', 'leaflet', 'maplibre-gl'],
          'storage': ['@ionic/storage', 'localforage-cordovasqlitedriver']
        }
      }
    },
    // Chunk size warning threshold
    chunkSizeWarningLimit: 1000
  },
  optimizeDeps: {
    // Pre-bundle dependencies untuk loading yang lebih cepat
    include: [
      '@ionic/react',
      '@ionic/react-router',
      'react',
      'react-dom',
      'react-router',
      'react-router-dom'
    ],
    exclude: [
      // Exclude heavy dependencies dari pre-bundling
      '@react-google-maps/api',
      'react-leaflet',
      'maplibre-gl',
      '@capacitor/camera',
      '@zxing/library'
    ]
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/setupTests.ts',
  }
})
