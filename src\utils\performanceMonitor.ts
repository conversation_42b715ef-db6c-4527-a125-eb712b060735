// Performance monitoring untuk tracking performa aplikasi
// Khususnya untuk perangkat low-end

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  networkLatency: number;
  frameRate: number;
  bundleSize: number;
}

interface PerformanceReport {
  timestamp: number;
  deviceInfo: any;
  metrics: PerformanceMetrics;
  userAgent: string;
  connectionType: string;
}

class PerformanceMonitor {
  private startTime: number;
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.startTime = performance.now();
    this.initializeMonitoring();
  }

  private initializeMonitoring(): void {
    // Monitor loading performance
    this.monitorLoadTime();
    
    // Monitor memory usage
    this.monitorMemoryUsage();
    
    // Monitor frame rate
    this.monitorFrameRate();
    
    // Monitor network performance
    this.monitorNetworkLatency();
  }

  private monitorLoadTime(): void {
    // Monitor navigation timing
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

      this.metrics.loadTime = navigation.loadEventEnd - navigation.fetchStart;
      this.metrics.renderTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;
      
      console.log('📊 Load Performance:', {
        loadTime: `${this.metrics.loadTime}ms`,
        renderTime: `${this.metrics.renderTime}ms`
      });
    });

    // Monitor resource loading
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        let totalSize = 0;
        
        entries.forEach((entry: any) => {
          if (entry.transferSize) {
            totalSize += entry.transferSize;
          }
        });
        
        this.metrics.bundleSize = totalSize;
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    }
  }

  private monitorMemoryUsage(): void {
    const checkMemory = () => {
      const nav = navigator as any;
      if (nav.memory) {
        this.metrics.memoryUsage = nav.memory.usedJSHeapSize / 1024 / 1024; // MB
        
        // Warning untuk memory usage tinggi
        if (this.metrics.memoryUsage > 50) {
          console.warn('⚠️ High memory usage detected:', `${this.metrics.memoryUsage.toFixed(2)}MB`);
        }
      }
    };

    // Check memory setiap 30 detik
    setInterval(checkMemory, 30000);
    checkMemory(); // Initial check
  }

  private monitorFrameRate(): void {
    let frameCount = 0;
    let lastTime = performance.now();
    
    const countFrames = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        this.metrics.frameRate = frameCount;
        
        // Warning untuk frame rate rendah
        if (this.metrics.frameRate < 30) {
          console.warn('⚠️ Low frame rate detected:', `${this.metrics.frameRate}fps`);
        }
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(countFrames);
    };
    
    requestAnimationFrame(countFrames);
  }

  private monitorNetworkLatency(): void {
    const measureLatency = async () => {
      const start = performance.now();
      
      try {
        // Ping ke server dengan request kecil
        await fetch('/favicon.png', { 
          method: 'HEAD',
          cache: 'no-cache'
        });
        
        this.metrics.networkLatency = performance.now() - start;
        
        // Warning untuk latency tinggi
        if (this.metrics.networkLatency > 1000) {
          console.warn('⚠️ High network latency:', `${this.metrics.networkLatency.toFixed(2)}ms`);
        }
      } catch (error) {
        console.warn('Network latency measurement failed:', error);
      }
    };

    // Measure latency setiap 60 detik
    setInterval(measureLatency, 60000);
    measureLatency(); // Initial measurement
  }

  // Generate performance report
  generateReport(): PerformanceReport {
    const nav = navigator as any;
    
    return {
      timestamp: Date.now(),
      deviceInfo: {
        memory: nav.deviceMemory || 'unknown',
        cores: nav.hardwareConcurrency || 'unknown',
        platform: nav.platform,
        userAgent: nav.userAgent
      },
      metrics: this.metrics as PerformanceMetrics,
      userAgent: nav.userAgent,
      connectionType: nav.connection?.effectiveType || 'unknown'
    };
  }

  // Send report ke server (opsional)
  async sendReport(): Promise<void> {
    const report = this.generateReport();
    
    try {
      await fetch('/api/performance-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(report)
      });
    } catch (error) {
      console.warn('Failed to send performance report:', error);
      
      // Simpan ke localStorage sebagai fallback
      const reports = JSON.parse(localStorage.getItem('performance_reports') || '[]');
      reports.push(report);
      
      // Keep only last 10 reports
      if (reports.length > 10) {
        reports.splice(0, reports.length - 10);
      }
      
      localStorage.setItem('performance_reports', JSON.stringify(reports));
    }
  }

  // Get current metrics
  getCurrentMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }

  // Performance recommendations
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.metrics.loadTime && this.metrics.loadTime > 3000) {
      recommendations.push('Loading time terlalu lambat. Pertimbangkan lazy loading dan code splitting.');
    }
    
    if (this.metrics.memoryUsage && this.metrics.memoryUsage > 100) {
      recommendations.push('Memory usage tinggi. Periksa memory leaks dan optimasi caching.');
    }
    
    if (this.metrics.frameRate && this.metrics.frameRate < 30) {
      recommendations.push('Frame rate rendah. Kurangi animasi dan efek visual yang berat.');
    }
    
    if (this.metrics.networkLatency && this.metrics.networkLatency > 2000) {
      recommendations.push('Network latency tinggi. Implementasi offline caching dan request optimization.');
    }
    
    if (this.metrics.bundleSize && this.metrics.bundleSize > 2 * 1024 * 1024) { // 2MB
      recommendations.push('Bundle size terlalu besar. Implementasi code splitting dan tree shaking.');
    }
    
    return recommendations;
  }

  // Cleanup
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions
export const performanceUtils = {
  // Measure function execution time
  measureExecutionTime: <T extends (...args: any[]) => any>(
    fn: T,
    label?: string
  ): T => {
    return ((...args: any[]) => {
      const start = performance.now();
      const result = fn(...args);
      const end = performance.now();
      
      console.log(`⏱️ ${label || fn.name} execution time: ${(end - start).toFixed(2)}ms`);
      
      return result;
    }) as T;
  },

  // Measure async function execution time
  measureAsyncExecutionTime: <T extends (...args: any[]) => Promise<any>>(
    fn: T,
    label?: string
  ): T => {
    return (async (...args: any[]) => {
      const start = performance.now();
      const result = await fn(...args);
      const end = performance.now();
      
      console.log(`⏱️ ${label || fn.name} async execution time: ${(end - start).toFixed(2)}ms`);
      
      return result;
    }) as T;
  },

  // Check if device is struggling
  isDeviceStruggling: (): boolean => {
    const metrics = performanceMonitor.getCurrentMetrics();
    
    return !!(
      (metrics.frameRate && metrics.frameRate < 20) ||
      (metrics.memoryUsage && metrics.memoryUsage > 150) ||
      (metrics.networkLatency && metrics.networkLatency > 3000)
    );
  },

  // Get performance grade
  getPerformanceGrade: (): 'A' | 'B' | 'C' | 'D' | 'F' => {
    const metrics = performanceMonitor.getCurrentMetrics();
    let score = 100;
    
    if (metrics.loadTime) {
      if (metrics.loadTime > 5000) score -= 30;
      else if (metrics.loadTime > 3000) score -= 15;
      else if (metrics.loadTime > 2000) score -= 5;
    }
    
    if (metrics.frameRate) {
      if (metrics.frameRate < 20) score -= 25;
      else if (metrics.frameRate < 30) score -= 10;
    }
    
    if (metrics.memoryUsage) {
      if (metrics.memoryUsage > 150) score -= 20;
      else if (metrics.memoryUsage > 100) score -= 10;
    }
    
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }
};

// Auto-send report setiap 5 menit
setInterval(() => {
  performanceMonitor.sendReport();
}, 5 * 60 * 1000);
