import { useState, useCallback, useRef } from 'react';
import { Geolocation } from '@capacitor/geolocation';

interface LocationData {
  lat: number;
  lng: number;
  timestamp: number;
  accuracy?: number;
}

interface UseOptimizedGeolocationReturn {
  coords: LocationData | null;
  loading: boolean;
  error: string | null;
  getLocation: () => Promise<LocationData>;
  clearError: () => void;
}

export function useOptimizedGeolocation(): UseOptimizedGeolocationReturn {
  const [coords, setCoords] = useState<LocationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const lastLocationRef = useRef<LocationData | null>(null);
  const lastFetchTimeRef = useRef<number>(0);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const getLocation = useCallback(async (): Promise<LocationData> => {
    const now = Date.now();
    
    // Cache location untuk 30 detik untuk mengurangi battery drain
    if (lastLocationRef.current && (now - lastFetchTimeRef.current) < 30000) {
      setCoords(lastLocationRef.current);
      return lastLocationRef.current;
    }

    setLoading(true);
    setError(null);
    
    try {
      // Check permission terlebih dahulu
      const perm = await Geolocation.checkPermissions();
      if (perm.location !== 'granted') {
        const req = await Geolocation.requestPermissions();
        if (req.location !== 'granted') {
          throw new Error('Izin lokasi ditolak. Silakan aktifkan izin lokasi di pengaturan aplikasi.');
        }
      }

      // Optimasi: gunakan timeout yang lebih pendek dan accuracy yang sesuai untuk low-end device
      const options = {
        enableHighAccuracy: true,
        timeout: 15000, // Kurangi timeout dari default 30s ke 15s
        maximumAge: 30000 // Cache selama 30 detik
      };

      const position = await Geolocation.getCurrentPosition(options);
      
      const locationData: LocationData = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
        timestamp: now,
        accuracy: position.coords.accuracy
      };

      // Update cache
      lastLocationRef.current = locationData;
      lastFetchTimeRef.current = now;
      setCoords(locationData);

      // Simpan ke localStorage untuk fallback
      try {
        localStorage.setItem('last_location', JSON.stringify(locationData));
      } catch (e) {
        console.warn('Failed to save location to localStorage:', e);
      }

      return locationData;
      
    } catch (e: any) {
      let errorMessage = 'Gagal mendapatkan lokasi';
      
      if (e.message?.includes('timeout')) {
        errorMessage = 'Timeout mendapatkan lokasi. Pastikan GPS aktif dan sinyal kuat.';
      } else if (e.message?.includes('denied') || e.message?.includes('ditolak')) {
        errorMessage = 'Izin lokasi ditolak. Silakan aktifkan izin lokasi di pengaturan aplikasi.';
      } else if (e.message?.includes('unavailable')) {
        errorMessage = 'Layanan lokasi tidak tersedia. Pastikan GPS aktif.';
      }
      
      setError(errorMessage);
      
      // Fallback ke lokasi terakhir jika ada
      if (lastLocationRef.current) {
        console.warn('Using cached location due to error:', e);
        setCoords(lastLocationRef.current);
        return lastLocationRef.current;
      }
      
      // Fallback ke localStorage
      try {
        const savedLocation = localStorage.getItem('last_location');
        if (savedLocation) {
          const parsed = JSON.parse(savedLocation);
          console.warn('Using localStorage location due to error:', e);
          setCoords(parsed);
          return parsed;
        }
      } catch (parseError) {
        console.warn('Failed to parse saved location:', parseError);
      }
      
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    coords,
    loading,
    error,
    getLocation,
    clearError
  };
}

// Hook untuk validasi lokasi dengan cache
export function useLocationValidator() {
  const [isValidating, setIsValidating] = useState(false);
  const validationCacheRef = useRef<Map<string, { valid: boolean; timestamp: number }>>(new Map());

  const validateLocation = useCallback(async (
    userLat: number, 
    userLng: number, 
    allowedLocations: Array<{ lat: number; lng: number; radius: number }>
  ): Promise<{ valid: boolean; message: string }> => {
    
    const cacheKey = `${userLat.toFixed(6)}_${userLng.toFixed(6)}`;
    const cached = validationCacheRef.current.get(cacheKey);
    
    // Cache validasi selama 5 menit
    if (cached && (Date.now() - cached.timestamp) < 300000) {
      return {
        valid: cached.valid,
        message: cached.valid ? 'Lokasi valid' : 'Anda berada di luar area yang diizinkan'
      };
    }

    setIsValidating(true);
    
    try {
      // Fungsi untuk menghitung jarak (Haversine formula - optimized)
      const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
        const R = 6371000; // Radius bumi dalam meter
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
      };

      // Cek apakah user berada dalam salah satu lokasi yang diizinkan
      const isValid = allowedLocations.some(location => {
        const distance = calculateDistance(userLat, userLng, location.lat, location.lng);
        return distance <= location.radius;
      });

      // Cache hasil validasi
      validationCacheRef.current.set(cacheKey, {
        valid: isValid,
        timestamp: Date.now()
      });

      return {
        valid: isValid,
        message: isValid ? 'Lokasi valid' : 'Anda berada di luar area yang diizinkan'
      };
      
    } finally {
      setIsValidating(false);
    }
  }, []);

  return {
    validateLocation,
    isValidating
  };
}
