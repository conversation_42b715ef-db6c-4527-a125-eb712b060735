import { Storage, Drivers } from '@ionic/storage';

let storageInstance: Storage | null = null;
let isInitialized = false;
const memoryCache = new Map<string, string>();

function safeParse(value: string): unknown {
  try {
    return JSON.parse(value);
  } catch {
    return value;
  }
}

export function getStorage(): Storage {
  if (!storageInstance) {
    throw new Error('Persistent storage belum diinisialisasi. Panggil initPersistentStorage() lebih dulu.');
  }
  return storageInstance;
}

/**
 * Inisialisasi storage berbasis SQLite (jika tersedia),
 * lalu lakukan mirroring 2 arah dengan localStorage:
 * - Saat start: data dari SQLite → localStorage
 * - Override setItem/removeItem: setiap perubahan localStorage → SQLite
 */
export async function initPersistentStorage(): Promise<void> {
  if (isInitialized) return;

  // Deteksi dan siapkan driver SQLite (jika ada) via dynamic import
  let sqliteDriverKey: string | null = null;
  let CordovaSQLiteDriver: any = null;
  try {
    const mod: any = (await import('localforage-cordovasqlitedriver')) as any;
    CordovaSQLiteDriver = mod?.default ?? mod;
    // @ts-ignore akses internal key driver (nama driver)
    sqliteDriverKey = CordovaSQLiteDriver?._driver || 'cordovaSQLiteDriver';
  } catch {
    // dev web: abaikan jika modul tidak tersedia
  }

  // Buat instance Storage dengan prioritas driver: SQLite -> IndexedDB -> LocalStorage
  const driverOrder: any[] = [];
  if (sqliteDriverKey) driverOrder.push(sqliteDriverKey);
  driverOrder.push(Drivers.IndexedDB, Drivers.LocalStorage);

  storageInstance = new Storage({
    name: 'absensipdam_db',
    driverOrder,
  });

  // Daftarkan driver SQLite sebelum create jika tersedia
  if (CordovaSQLiteDriver) {
    try {
      await storageInstance.defineDriver(CordovaSQLiteDriver);
    } catch {
      // abaikan
    }
  }

  await storageInstance.create();

  // 1) Restore dari storage → localStorage agar kode lama tetap bekerja
  try {
    await storageInstance.forEach((value: unknown, key: string) => {
      try {
        const str = typeof value === 'string' ? value : JSON.stringify(value);
        // Isi cache in-memory sebagai sumber sinkron
        memoryCache.set(key, str);
        window.localStorage.setItem(key, str);
      } catch {
        // abaikan key yang gagal diparse/ditulis
      }
    });
  } catch {
    // abaikan
  }

  // 2) Seed awal localStorage → storage (untuk pertama kali install)
  try {
    for (let i = 0; i < window.localStorage.length; i++) {
      const key = window.localStorage.key(i);
      if (!key) continue;
      const val = window.localStorage.getItem(key);
      if (val == null) continue;
      await storageInstance.set(key, safeParse(val));
      // Pastikan cache ikut terisi
      memoryCache.set(key, val);
    }
  } catch {
    // abaikan
  }

  // 3) Hook: pantau perubahan berikutnya di localStorage → simpan juga ke storage
  try {
    const originalSetItem = window.localStorage.setItem.bind(window.localStorage);
    const originalRemoveItem = window.localStorage.removeItem.bind(window.localStorage);
    const originalGetItem = window.localStorage.getItem.bind(window.localStorage);
    const originalClear = window.localStorage.clear.bind(window.localStorage);

    window.localStorage.setItem = (key: string, value: string) => {
      originalSetItem(key, value);
      // Simpan asinkron ke storage; tidak menunggu agar tidak blok UI
      void storageInstance!.set(key, safeParse(value));
      // Update cache sinkron
      memoryCache.set(key, value);
    };

    window.localStorage.removeItem = (key: string) => {
      originalRemoveItem(key);
      void storageInstance!.remove(key);
      memoryCache.delete(key);
    };

    // Baca dari cache in-memory agar tetap tersedia walau WebView menghapus localStorage
    window.localStorage.getItem = (key: string): string | null => {
      if (memoryCache.has(key)) return memoryCache.get(key)!;
      return originalGetItem(key);
    };

    // Jika ada pemanggilan clear() dari kode, sinkronkan ke SQLite dan cache
    window.localStorage.clear = () => {
      originalClear();
      memoryCache.clear();
      // Tidak menghapus semua kunci di SQLite untuk keamanan; jika ingin, dapat diaktifkan:
      // void storageInstance!.clear();
    };
  } catch {
    // jika tidak bisa meng-override, abaikan
  }

  isInitialized = true;
}


