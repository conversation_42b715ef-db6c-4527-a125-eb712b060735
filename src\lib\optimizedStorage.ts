// Optimized storage untuk perangkat low-end
// Mengurangi overhead dan mempercepat akses data

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiry?: number;
}

class OptimizedStorage {
  private memoryCache = new Map<string, any>();
  private readonly CACHE_EXPIRY = 5 * 60 * 1000; // 5 menit default
  private readonly MAX_CACHE_SIZE = 50; // Batasi ukuran cache untuk memory efficiency
  
  // Lazy loading untuk storage instance
  private storageInstance: any = null;
  private storagePromise: Promise<any> | null = null;

  private async getStorageInstance() {
    if (this.storageInstance) {
      return this.storageInstance;
    }

    if (!this.storagePromise) {
      this.storagePromise = this.initStorage();
    }

    return this.storagePromise;
  }

  private async initStorage() {
    try {
      // Dynamic import untuk mengurangi bundle size
      const { Storage, Drivers } = await import('@ionic/storage');
      
      this.storageInstance = new Storage({
        name: 'absensipdam_optimized',
        driverOrder: [Drivers.IndexedDB, Drivers.LocalStorage] // Skip SQLite untuk simplicity
      });

      await this.storageInstance.create();
      return this.storageInstance;
    } catch (error) {
      console.warn('Storage initialization failed, using localStorage fallback:', error);
      // Fallback ke localStorage wrapper
      return {
        get: (key: string) => {
          try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
          } catch {
            return null;
          }
        },
        set: (key: string, value: any) => {
          try {
            localStorage.setItem(key, JSON.stringify(value));
          } catch (e) {
            console.warn('localStorage set failed:', e);
          }
        },
        remove: (key: string) => {
          try {
            localStorage.removeItem(key);
          } catch (e) {
            console.warn('localStorage remove failed:', e);
          }
        }
      };
    }
  }

  // Get dengan memory cache
  async get<T>(key: string, useCache = true): Promise<T | null> {
    // Cek memory cache terlebih dahulu
    if (useCache && this.memoryCache.has(key)) {
      const cached = this.memoryCache.get(key) as CacheItem<T>;
      if (!cached.expiry || Date.now() < cached.expiry) {
        return cached.data;
      } else {
        this.memoryCache.delete(key);
      }
    }

    try {
      const storage = await this.getStorageInstance();
      const data = await storage.get(key);
      
      // Cache ke memory jika data ada
      if (data !== null && useCache) {
        this.setMemoryCache(key, data);
      }
      
      return data;
    } catch (error) {
      console.warn(`Storage get failed for key ${key}:`, error);
      return null;
    }
  }

  // Set dengan memory cache
  async set<T>(key: string, value: T, cacheExpiry?: number): Promise<void> {
    try {
      const storage = await this.getStorageInstance();
      await storage.set(key, value);
      
      // Update memory cache
      this.setMemoryCache(key, value, cacheExpiry);
    } catch (error) {
      console.warn(`Storage set failed for key ${key}:`, error);
    }
  }

  // Remove dari storage dan cache
  async remove(key: string): Promise<void> {
    this.memoryCache.delete(key);
    
    try {
      const storage = await this.getStorageInstance();
      await storage.remove(key);
    } catch (error) {
      console.warn(`Storage remove failed for key ${key}:`, error);
    }
  }

  // Memory cache management
  private setMemoryCache<T>(key: string, data: T, expiry?: number): void {
    // Batasi ukuran cache
    if (this.memoryCache.size >= this.MAX_CACHE_SIZE) {
      // Hapus item tertua
      const firstKey = this.memoryCache.keys().next().value;
      if (firstKey) {
        this.memoryCache.delete(firstKey);
      }
    }

    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiry: expiry ? Date.now() + expiry : Date.now() + this.CACHE_EXPIRY
    };

    this.memoryCache.set(key, cacheItem);
  }

  // Clear expired cache
  clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, item] of this.memoryCache.entries()) {
      if (item.expiry && now >= item.expiry) {
        this.memoryCache.delete(key);
      }
    }
  }

  // Get cache stats untuk debugging
  getCacheStats() {
    return {
      size: this.memoryCache.size,
      maxSize: this.MAX_CACHE_SIZE,
      keys: Array.from(this.memoryCache.keys())
    };
  }

  // Batch operations untuk efisiensi
  async batchGet<T>(keys: string[]): Promise<Record<string, T | null>> {
    const results: Record<string, T | null> = {};
    
    // Cek memory cache terlebih dahulu
    const uncachedKeys: string[] = [];
    for (const key of keys) {
      if (this.memoryCache.has(key)) {
        const cached = this.memoryCache.get(key) as CacheItem<T>;
        if (!cached.expiry || Date.now() < cached.expiry) {
          results[key] = cached.data;
          continue;
        } else {
          this.memoryCache.delete(key);
        }
      }
      uncachedKeys.push(key);
    }

    // Fetch uncached keys dari storage
    if (uncachedKeys.length > 0) {
      try {
        const storage = await this.getStorageInstance();
        await Promise.all(
          uncachedKeys.map(async (key) => {
            try {
              const data = await storage.get(key);
              results[key] = data;
              if (data !== null) {
                this.setMemoryCache(key, data);
              }
            } catch (error) {
              console.warn(`Batch get failed for key ${key}:`, error);
              results[key] = null;
            }
          })
        );
      } catch (error) {
        console.warn('Batch get failed:', error);
        // Set remaining keys to null
        for (const key of uncachedKeys) {
          if (!(key in results)) {
            results[key] = null;
          }
        }
      }
    }

    return results;
  }

  async batchSet<T>(items: Record<string, T>): Promise<void> {
    try {
      const storage = await this.getStorageInstance();
      await Promise.all(
        Object.entries(items).map(async ([key, value]) => {
          try {
            await storage.set(key, value);
            this.setMemoryCache(key, value);
          } catch (error) {
            console.warn(`Batch set failed for key ${key}:`, error);
          }
        })
      );
    } catch (error) {
      console.warn('Batch set failed:', error);
    }
  }
}

// Singleton instance
export const optimizedStorage = new OptimizedStorage();

// Utility functions untuk common operations
export const storageUtils = {
  // Cache user data
  async cacheUserData(userData: any): Promise<void> {
    await optimizedStorage.set('user_cache', userData, 24 * 60 * 60 * 1000); // 24 jam
  },

  async getCachedUserData(): Promise<any> {
    return optimizedStorage.get('user_cache');
  },

  // Cache lokasi data
  async cacheLokasiData(lokasiData: any[]): Promise<void> {
    await optimizedStorage.set('lokasi_cache', lokasiData, 60 * 60 * 1000); // 1 jam
  },

  async getCachedLokasiData(): Promise<any[]> {
    return optimizedStorage.get('lokasi_cache') || [];
  },

  // Cache jam kerja data
  async cacheJamKerjaData(jamKerjaData: any[]): Promise<void> {
    await optimizedStorage.set('jam_kerja_cache', jamKerjaData, 60 * 60 * 1000); // 1 jam
  },

  async getCachedJamKerjaData(): Promise<any[]> {
    return optimizedStorage.get('jam_kerja_cache') || [];
  },

  // Cleanup old data
  async cleanupOldData(): Promise<void> {
    const storage = await optimizedStorage['getStorageInstance']();
    const keysToCheck = ['offline_absensi_queue', 'absensi_backup', 'gps_queue'];
    
    for (const key of keysToCheck) {
      try {
        const data = await storage.get(key);
        if (Array.isArray(data)) {
          // Hapus data yang lebih dari 7 hari
          const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
          const filteredData = data.filter((item: any) => {
            const itemTime = new Date(item.tanggal || item.timestamp || 0).getTime();
            return itemTime > sevenDaysAgo;
          });
          
          if (filteredData.length !== data.length) {
            await storage.set(key, filteredData);
          }
        }
      } catch (error) {
        console.warn(`Cleanup failed for key ${key}:`, error);
      }
    }
    
    // Clear expired memory cache
    optimizedStorage.clearExpiredCache();
  }
};

// Auto cleanup setiap 30 menit
setInterval(() => {
  storageUtils.cleanupOldData();
}, 30 * 60 * 1000);
