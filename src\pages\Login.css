.login-bg {
  --background: linear-gradient(135deg, #eef2ff 0%, #f0fdfa 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-hero {
  position: absolute;
  inset: 0 0 auto 0;
  height: 36vh;
  background: linear-gradient(135deg, #1880ff 60%, #005be7 100%);
  border-bottom-left-radius: 48px;
  border-bottom-right-radius: 48px;
  box-shadow: 0 8px 32px rgba(24,128,255,0.13);
  overflow: hidden;
}

.login-blob {
  position: absolute;
  border-radius: 50%;
  opacity: 0.15;
  filter: blur(20px);
}
.login-blob-1 { width: 220px; height: 220px; background: #fff; top: -40px; right: -30px; }
.login-blob-2 { width: 280px; height: 280px; background: #c7d2fe; bottom: -80px; left: -60px; }

.login-brand {
  position: relative;
  height: 100%;
  max-width: 420px;
  margin: 0 auto;
  padding: 24px 16px;
  display: flex;
  align-items: center;
  gap: 14px;
  color: #fff;
}

.login-container {
  max-width: 420px;
  margin: 0 auto;
  padding-top: 28vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-logo {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  box-shadow: 0 6px 22px rgba(0,0,0,0.18);
  background: #ffffff;
}

.login-title {
  font-size: 1.8rem;
  font-weight: 800;
  margin: 0;
}

.login-desc {
  color: #e0e7ff;
  font-size: 1rem;
  margin: 2px 0 0 0;
}

.login-card {
  width: 100%;
  box-shadow: 0 4px 24px rgba(0,0,0,0.07);
  border-radius: 18px;
  border: 1px solid rgba(24,128,255,0.12);
}

.glass {
  background: rgba(255, 255, 255, 0.88) !important;
  -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
}

.login-input {
  margin-bottom: 12px;
}

.login-input .input-wrapper.sc-ion-input-md {
  display: flex;
  align-items: center;
}

.login-input ion-icon {
  font-size: 1.5em;
  margin-right: 6px;
  color: #6366f1;
  align-self: center;
}

.login-input .label-floating.sc-ion-label-md {
  display: flex;
  align-items: center;
  height: 100%;
  margin-left: 0.2em;
}

.login-input .sc-ion-label-md-h {
  display: flex;
  align-items: center;
  height: 100%;
}

.login-btn {
  font-weight: 600;
  letter-spacing: 0.5px;
  font-size: 1.1rem;
  border-radius: 24px;
  transition: background 0.2s;
}
.login-btn.primary { box-shadow: 0 10px 24px rgba(24,128,255,0.18); transition: transform .12s; }
.login-btn.primary:hover { transform: translateY(-1px); box-shadow: 0 12px 28px rgba(24,128,255,0.24); }
.login-btn.primary:active { transform: translateY(0); }

.login-list {
  margin-bottom: 8px;
}

.login-input-item {
  margin-bottom: 16px;
  border-radius: 12px;
  --background: #f8fafc;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  min-height: 54px;
}

.input-with-icon { padding: 6px; border: 1px solid #e5e7eb; transition: box-shadow .2s, border-color .2s; }
.input-with-icon:focus-within { border-color: #1880ff; box-shadow: 0 0 0 4px rgba(24,128,255,0.1); }
.stacked-field { flex: 1; display: flex; flex-direction: column; }
.password-field { display: flex; align-items: center; gap: 6px; position: relative; }
.password-field ion-input .native-input,
.password-field .native-input.sc-ion-input-md { padding-right: 44px; }

.eye-click-area {
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  padding: 0;
}
.eye-click-area:disabled { opacity: .6; }

.login-input-icon {
  font-size: 1.4em;
  color: #6366f1;
  margin-right: 4px;
  align-self: flex-start;
  margin-top: 8px;
}

.login-input-item .sc-ion-label-md-h, .login-input-item .label-floating.sc-ion-label-md {
  align-items: flex-start !important;
  margin-top: 8px;
  font-size: 1em;
}

.login-input-item .label-floating.sc-ion-label-md {
  transform: translateY(-6px);
}

.login-input-item .native-input.sc-ion-input-md {
  padding-top: 12px;
  padding-bottom: 8px;
  font-size: 1.08em;
}

.eye-btn { margin-left: 6px; --padding-start: 8px; --padding-end: 8px; }
.eye-btn ion-icon { font-size: 1.4rem; color: #64748b; }
.eye-btn:hover ion-icon { color: #6366f1; }

.login-card-title { display: flex; align-items: center; gap: 8px; }
.login-card-title-icon { font-size: 1.2rem; color: #1880ff; }

@media (max-width: 480px) {
  .login-container {
    max-width: 94vw;
    padding-top: 24vh;
  }
  .login-card {
    border-radius: 12px;
  }
  .login-title {
    font-size: 1.2rem;
  }
} 