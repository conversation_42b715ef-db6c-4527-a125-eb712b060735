import React, { useState } from 'react';
import {
  IonPage, IonContent, IonInput, IonItem, IonLabel, IonButton, IonToast, IonLoading, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonIcon, IonText, IonList
} from '@ionic/react';
import { personCircleOutline, lockClosedOutline, logInOutline, eyeOutline, eyeOffOutline } from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import './Login.css';
import { v4 as uuidv4 } from 'uuid';

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const history = useHistory();

  const getDeviceId = () => {
    let deviceId = localStorage.getItem('device_id');
    if (!deviceId) {
      deviceId = uuidv4();
      localStorage.setItem('device_id', deviceId);
    }
    return deviceId;
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const deviceId = getDeviceId();
      // 1. Cek status akun user
      const statusUrl = `https://absensiku.trunois.my.id/api/status_user.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(username)}&device_id=${encodeURIComponent(deviceId)}`;
      const statusRes = await fetch(statusUrl);
      const statusJson = await statusRes.json();
      if (statusJson.status === 'success') {
        const statusData = statusJson.data;
        if (Array.isArray(statusData) && statusData.length > 0) {
          const userStatus = statusData[0];
          if (userStatus.status_akun === 'blokir') {
            setToastMessage('Akun Anda diblokir, silakan hubungi admin');
            setShowToast(true);
            setLoading(false);
            return;
          }
          if (userStatus.status_login === 'login') {
            // Jika device_id sama, izinkan login
            if (userStatus.device_id && userStatus.device_id === deviceId) {
              // lanjutkan login
            } else {
              setToastMessage('Akun sudah login di perangkat lain, tidak bisa login lagi');
              setShowToast(true);
              setLoading(false);
              return;
            }
          }
        }
        // Jika data kosong atau status_login = 'tidak' dan status_akun = 'aktif', lanjutkan login
      } else {
        setToastMessage('Gagal cek status akun');
        setShowToast(true);
        setLoading(false);
        return;
      }

      // 2. Proses login seperti biasa
      const url = `https://absensiku.trunois.my.id/api/karyawan.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`;
      const response = await fetch(url);
      const result = await response.json();
      if (result.status === 'success') {
        // Simpan data user ke localStorage sebagai objek tunggal (bukan array)
        const normalizedUser = Array.isArray(result.data) ? result.data[0] : result.data;
        localStorage.setItem('user', JSON.stringify(normalizedUser));

        // Kirim status login ke backend
        try {
          // Cek apakah data status_user sudah ada
          const statusUrl = `https://absensiku.trunois.my.id/api/status_user.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(username)}`;
          const statusRes = await fetch(statusUrl);
          const statusJson = await statusRes.json();
          let method = 'POST';
          let bodyData: any = {
            api_key: 'absensiku_api_key_2023',
            nik: username,
            status_login: 'login',
            status_akun: 'aktif',
            device_id: deviceId,
          };
          let url = 'https://absensiku.trunois.my.id/api/status_user.php';
          if (statusJson.status === 'success' && Array.isArray(statusJson.data) && statusJson.data.length > 0) {
            // Sudah ada data, update (PUT)
            method = 'PUT';
            bodyData.id = statusJson.data[0].id;
          }
          await fetch(url, {
            method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(bodyData),
          });
        } catch (e) {
          // Optional: bisa tampilkan toast jika gagal update status login
        }

        history.push('/home');
      } else {
        setToastMessage(result.message || 'NIK atau password salah');
        setShowToast(true);
      }
    } catch (error) {
      setToastMessage('Terjadi kesalahan koneksi');
      setShowToast(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <IonPage>
      <IonContent className="ion-padding login-bg" fullscreen>
        <div className="login-hero">
          <div className="login-blob login-blob-1" />
          <div className="login-blob login-blob-2" />
          <div className="login-brand">
            <img src="/logo192.png" alt="Logo" className="login-logo" />
            <div>
              <h1 className="login-title">Absensi Karyawan</h1>
              <p className="login-desc">Masuk untuk melanjutkan aktivitas Anda</p>
            </div>
          </div>
        </div>

        <div className="login-container">
          <IonCard className="login-card glass">
            <IonCardHeader>
              <IonCardTitle className="login-card-title">
                <IonIcon icon={logInOutline} className="login-card-title-icon" />
                Masuk Akun
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <form onSubmit={handleLogin} autoComplete="off">
                <IonList lines="none" className="login-list">
                  <IonItem className="login-input-item input-with-icon">
                    <IonIcon icon={personCircleOutline} slot="start" className="login-input-icon" />
                    <div className="stacked-field">
                      <IonLabel position="stacked">NIK</IonLabel>
                      <IonInput
                        value={username}
                        onIonChange={e => setUsername(e.detail.value || '')}
                        required
                        inputmode="numeric"
                        placeholder="Masukkan NIK"
                      />
                    </div>
                  </IonItem>

                  <IonItem className="login-input-item input-with-icon">
                    <IonIcon icon={lockClosedOutline} slot="start" className="login-input-icon" />
                    <div className="stacked-field">
                      <IonLabel position="stacked">Password</IonLabel>
                      <div className="password-field">
                        <IonInput
                          type={showPassword ? 'text' : 'password'}
                          value={password}
                          onIonChange={e => setPassword(e.detail.value || '')}
                          required
                          placeholder="Masukkan password"
                        />
                      </div>
                    </div>
                    <IonButton
                      slot="end"
                      fill="clear"
                      className="eye-btn"
                      onClick={() => setShowPassword(v => !v)}
                      disabled={loading}
                      aria-label={showPassword ? 'Sembunyikan Password' : 'Lihat Password'}
                    >
                      <IonIcon icon={showPassword ? eyeOffOutline : eyeOutline} />
                    </IonButton>
                  </IonItem>
                </IonList>

                <IonButton
                  expand="block"
                  type="submit"
                  className="ion-margin-top login-btn primary"
                  color="primary"
                  shape="round"
                  disabled={loading}
                >
                  <IonIcon icon={logInOutline} slot="start" />
                  {loading ? 'Memproses...' : 'Login'}
                </IonButton>

                <div className="login-footnote">
                  <IonIcon icon={lockClosedOutline} />
                  <span>Data Anda aman dan terenkripsi.</span>
                </div>
              </form>
            </IonCardContent>
          </IonCard>
        </div>

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={2000}
          color="danger"
        />
        <IonLoading isOpen={loading} message="Memproses..." />
      </IonContent>
    </IonPage>
  );
};

export default Login; 