// Performance optimizer untuk perangkat low-end
// Mengatur berbagai optimasi berdasarkan kemampuan perangkat

interface DeviceCapabilities {
  isLowEnd: boolean;
  memoryGB: number;
  cores: number;
  connectionType: string;
}

class PerformanceOptimizer {
  private deviceCapabilities: DeviceCapabilities;
  private optimizationSettings: any = {};

  constructor() {
    this.deviceCapabilities = this.detectDeviceCapabilities();
    this.applyOptimizations();
  }

  private detectDeviceCapabilities(): DeviceCapabilities {
    // Deteksi kemampuan perangkat
    const navigator = window.navigator as any;
    
    // Estimasi memory (fallback jika tidak tersedia)
    const memoryGB = navigator.deviceMemory || this.estimateMemory();
    
    // Estimasi CPU cores
    const cores = navigator.hardwareConcurrency || 2;
    
    // Deteksi connection type
    const connection = (navigator.connection || navigator.mozConnection || navigator.webkitConnection);
    const connectionType = connection?.effectiveType || 'unknown';
    
    // Tentukan apakah perangkat low-end
    const isLowEnd = memoryGB <= 2 || cores <= 2 || ['slow-2g', '2g'].includes(connectionType);

    return {
      isLowEnd,
      memoryGB,
      cores,
      connectionType
    };
  }

  private estimateMemory(): number {
    // Estimasi memory berdasarkan user agent dan performance
    const userAgent = navigator.userAgent.toLowerCase();
    
    // Perangkat yang dikenal low-end
    if (userAgent.includes('android') && userAgent.includes('wv')) {
      // Android WebView biasanya di perangkat budget
      return 1;
    }
    
    // Default estimation
    return 2;
  }

  private applyOptimizations(): void {
    if (this.deviceCapabilities.isLowEnd) {
      this.optimizationSettings = {
        // Rendering optimizations
        enableVirtualScrolling: true,
        reduceAnimations: true,
        lowerImageQuality: true,
        
        // Network optimizations
        enableRequestDebouncing: true,
        reduceConcurrentRequests: true,
        enableDataCompression: true,
        
        // Memory optimizations
        enableAggressiveCaching: false, // Paradoks: cache bisa memakan memory
        enableLazyLoading: true,
        reduceComponentComplexity: true,
        
        // Camera optimizations
        cameraMaxResolution: { width: 640, height: 480 },
        cameraFrameRate: 15,
        imageCompressionQuality: 0.6,
        
        // Location optimizations
        locationCacheTime: 60000, // 1 menit
        locationTimeout: 10000, // 10 detik
        locationAccuracy: false, // Disable high accuracy
        
        // UI optimizations
        disableTransitions: true,
        reduceBlurEffects: true,
        simplifyGradients: true
      };
    } else {
      this.optimizationSettings = {
        enableVirtualScrolling: false,
        reduceAnimations: false,
        lowerImageQuality: false,
        enableRequestDebouncing: false,
        reduceConcurrentRequests: false,
        enableDataCompression: false,
        enableAggressiveCaching: true,
        enableLazyLoading: true,
        reduceComponentComplexity: false,
        cameraMaxResolution: { width: 1280, height: 720 },
        cameraFrameRate: 30,
        imageCompressionQuality: 0.8,
        locationCacheTime: 30000,
        locationTimeout: 15000,
        locationAccuracy: true,
        disableTransitions: false,
        reduceBlurEffects: false,
        simplifyGradients: false
      };
    }

    // Apply CSS optimizations
    this.applyCSSOptimizations();
  }

  private applyCSSOptimizations(): void {
    const style = document.createElement('style');
    
    if (this.deviceCapabilities.isLowEnd) {
      style.textContent = `
        /* Low-end device optimizations */
        * {
          will-change: auto !important;
        }
        
        .ion-page {
          transform: translateZ(0);
        }
        
        /* Disable expensive animations */
        ${this.optimizationSettings.disableTransitions ? `
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
        ` : ''}
        
        /* Simplify gradients */
        ${this.optimizationSettings.simplifyGradients ? `
        .loading-screen {
          --background: #667eea !important;
        }
        ` : ''}
        
        /* Reduce blur effects */
        ${this.optimizationSettings.reduceBlurEffects ? `
        .backdrop-blur {
          backdrop-filter: none !important;
        }
        ` : ''}
      `;
    }
    
    document.head.appendChild(style);
  }

  // Public methods untuk mendapatkan settings
  getOptimizationSettings() {
    return this.optimizationSettings;
  }

  getDeviceCapabilities() {
    return this.deviceCapabilities;
  }

  isLowEndDevice(): boolean {
    return this.deviceCapabilities.isLowEnd;
  }

  // Optimasi untuk request network
  optimizeNetworkRequest(url: string, options: RequestInit = {}): RequestInit {
    if (this.optimizationSettings.enableDataCompression) {
      options.headers = {
        ...options.headers,
        'Accept-Encoding': 'gzip, deflate, br'
      };
    }

    if (this.optimizationSettings.reduceConcurrentRequests) {
      // Note: Queue implementation would need to be handled at fetch level
      console.log('Request queuing enabled for:', url);
    }

    return options;
  }

  // Request queue implementation (simplified)
  private requestQueue: Array<() => void> = [];
  private activeRequests = 0;
  private maxConcurrentRequests = 2;

  // Helper method untuk queue management (tidak digunakan langsung di optimizeNetworkRequest)
  private processRequestQueue(): void {
    if (this.requestQueue.length > 0 && this.activeRequests < this.maxConcurrentRequests) {
      const nextRequest = this.requestQueue.shift();
      if (nextRequest) {
        this.activeRequests++;
        nextRequest();

        // Simulate request completion
        setTimeout(() => {
          this.activeRequests--;
          this.processRequestQueue();
        }, 1000);
      }
    }
  }

  // Optimasi untuk image loading
  optimizeImageSrc(originalSrc: string, maxWidth?: number): string {
    if (!this.optimizationSettings.lowerImageQuality) {
      return originalSrc;
    }

    // Add query parameters untuk image optimization
    const url = new URL(originalSrc, window.location.origin);
    url.searchParams.set('quality', '60');
    
    if (maxWidth) {
      url.searchParams.set('width', maxWidth.toString());
    }

    return url.toString();
  }

  // Debounce utility untuk input
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  // Throttle utility untuk scroll events
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // Memory cleanup utility
  cleanup(): void {
    // Clear any intervals or timeouts
    this.requestQueue = [];
    this.activeRequests = 0;
  }
}

// Singleton instance
export const performanceOptimizer = new PerformanceOptimizer();

// Export utilities
export const {
  getOptimizationSettings,
  getDeviceCapabilities,
  isLowEndDevice,
  optimizeNetworkRequest,
  optimizeImageSrc,
  debounce,
  throttle
} = performanceOptimizer;
