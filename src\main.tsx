import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import { initPersistentStorage } from './lib/persistentStorage';

const container = document.getElementById('root');
const root = createRoot(container!);

// Pastikan storage persisten siap sebelum render App,
// agar pembacaan awal localStorage (mis. status login) sudah ter-hydrate.
initPersistentStorage()
  .catch(() => {
    // Abaikan kegagalan init: app tetap dirender, storage fallback ke IndexedDB/LocalStorage
  })
  .finally(() => {
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );
  });