import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';

const container = document.getElementById('root');
const root = createRoot(container!);

// Render app immediately untuk loading yang lebih cepat
// Storage initialization akan dilakukan secara lazy di background
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// Initialize storage di background setelah app render
// Ini mencegah blocking pada startup
setTimeout(async () => {
  try {
    const { initPersistentStorage } = await import('./lib/persistentStorage');
    await initPersistentStorage();
  } catch (error) {
    console.log('Storage initialization failed, using fallback:', error);
  }
}, 100); // Delay minimal untuk memastikan app sudah render