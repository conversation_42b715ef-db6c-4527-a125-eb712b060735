import React, { useRef, useState, useCallback, useEffect } from 'react';
import { IonButton, IonIcon, IonText } from '@ionic/react';
import { cameraOutline, refreshOutline } from 'ionicons/icons';

interface OptimizedCameraProps {
  onPhotoTaken: (photo: string) => void;
  disabled?: boolean;
}

const OptimizedCamera: React.FC<OptimizedCameraProps> = ({ onPhotoTaken, disabled = false }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  
  const [photo, setPhoto] = useState<string | null>(null);
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // Cleanup stream saat component unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  }, []);

  const startCamera = useCallback(async () => {
    setError('');
    setPhoto(null);
    setIsLoading(true);
    
    try {
      // Optimasi: gunakan constraint yang lebih ringan untuk perangkat low-end
      const constraints = {
        video: {
          facingMode: 'user',
          width: { ideal: 640, max: 1280 }, // Batasi resolusi untuk performa
          height: { ideal: 480, max: 720 },
          frameRate: { ideal: 15, max: 30 } // Kurangi frame rate untuk performa
        }
      };
      
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        // Tunggu video ready sebelum menghilangkan loading
        videoRef.current.onloadedmetadata = () => {
          setIsLoading(false);
        };
      }
    } catch (err: any) {
      setError('Tidak dapat mengakses kamera. Pastikan izin kamera sudah diberikan.');
      setIsLoading(false);
      console.error('Camera error:', err);
    }
  }, []);

  const takePhoto = useCallback(() => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      // Optimasi: gunakan resolusi yang lebih kecil untuk kompresi
      const maxWidth = 800;
      const maxHeight = 600;
      
      let { videoWidth, videoHeight } = video;
      
      // Scale down jika terlalu besar
      if (videoWidth > maxWidth || videoHeight > maxHeight) {
        const ratio = Math.min(maxWidth / videoWidth, maxHeight / videoHeight);
        videoWidth *= ratio;
        videoHeight *= ratio;
      }
      
      canvas.width = videoWidth;
      canvas.height = videoHeight;
      
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0, videoWidth, videoHeight);
        
        // Optimasi: gunakan kualitas JPEG yang lebih rendah untuk ukuran file lebih kecil
        const photoData = canvas.toDataURL('image/jpeg', 0.7); // 70% quality
        setPhoto(photoData);
        onPhotoTaken(photoData);
      }
      stopCamera();
    }
  }, [onPhotoTaken, stopCamera]);

  const retakePhoto = useCallback(() => {
    setPhoto(null);
    startCamera();
  }, [startCamera]);

  return (
    <div style={{ textAlign: 'center' }}>
      {!photo && (
        <div style={{ position: 'relative', marginBottom: '16px' }}>
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            style={{
              width: '100%',
              maxWidth: '400px',
              height: 'auto',
              borderRadius: '8px',
              backgroundColor: '#000'
            }}
          />
          {isLoading && (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              color: 'white',
              fontSize: '14px'
            }}>
              Memuat kamera...
            </div>
          )}
        </div>
      )}
      
      {photo && (
        <div style={{ marginBottom: '16px' }}>
          <img
            src={photo}
            alt="Foto yang diambil"
            style={{
              width: '100%',
              maxWidth: '400px',
              height: 'auto',
              borderRadius: '8px'
            }}
          />
        </div>
      )}
      
      <canvas ref={canvasRef} style={{ display: 'none' }} />
      
      {error && (
        <IonText color="danger">
          <p style={{ fontSize: '14px', margin: '8px 0' }}>{error}</p>
        </IonText>
      )}
      
      <div style={{ display: 'flex', justifyContent: 'center', gap: '12px', margin: '18px 0' }}>
        {!photo ? (
          <>
            {!streamRef.current && (
              <IonButton 
                color="primary" 
                onClick={startCamera} 
                size="default" 
                shape="round"
                disabled={disabled || isLoading}
              >
                <IonIcon icon={cameraOutline} slot="start" /> 
                {isLoading ? 'Memuat...' : 'Buka Kamera'}
              </IonButton>
            )}
            {streamRef.current && !isLoading && (
              <IonButton 
                color="primary" 
                onClick={takePhoto} 
                size="default" 
                shape="round"
                disabled={disabled}
              >
                <IonIcon icon={cameraOutline} slot="start" /> 
                Ambil Foto
              </IonButton>
            )}
          </>
        ) : (
          <IonButton 
            color="medium" 
            onClick={retakePhoto} 
            size="default" 
            shape="round"
            disabled={disabled}
          >
            <IonIcon icon={refreshOutline} slot="start" /> 
            Ulangi
          </IonButton>
        )}
      </div>
    </div>
  );
};

export default OptimizedCamera;
